网格化小程序需求草稿

网格划分为四级，一级为公司党委、二级为支部、三级为党小组、四级为党员骨干，各级别查阅权限不一样，审批权限不一样。

 

 

一、信息填报

分为职工个人档案、关注等级、历史行为记录（提交了多少条问题、解决了多少条问题）等内容

自行填报

功能需求：可直接调取钉钉数据，避免过度收集信息；支持扫码填写、后台导入、管理员修改、信息下载等；根据填报信息实现关注等级的自动分类，后期可手动调整；根据网格信息系统自动分理人员，减少手动输入网格；关注等级查阅需设置权限

管理自行更新（）

二、问题提报

职工提交问题，支持附件上传。问题在每一级别可终止处理也可层层上报，根据不同问题分类流转至不同人员。

 

***\*需求：\****能否满足向上提报或终止问题时条件不一样；能否保留处理跟踪记录，部分管理人员可实时查看进度；问题处理结果抄送至提报人

三、数据分析

系统能否每月自动抓取员工思想动态、风险隐患分布、诉求热点变化，问题提报率、解决率等数据，形成趋势图及各种数据分析图表，下载时可根据不同需要勾选不同数据； 

能否生成专项分析报告，精准呈现核心诉求领域、高发问题单位/岗位分布、特定群体关注度追踪、各网格风险值度评估等核心指标

能否生成问题台账清单

| 提报时间 | 提报人 | 问题内容 | 四级网格 | 三级网格 | 二级网格 | 一级网格 |
| -------- | ------ | -------- | -------- | -------- | -------- | -------- |
| …        | …      | …        | √        |          |          |          |
| …        | …      | …        | X        | √        |          |          |
| …        | …      | …        | X        |          | √        |          |

公司级别问题台账清单更关注问题在哪一层级解决

| 提报时间 | 提报人 | 问题内容 | 机关一支部四级网格1 | 机关一支部三级网格2 | 机关一支部二级网格 | 机关一支部一级网格 |
| -------- | ------ | -------- | ------------------- | ------------------- | ------------------ | ------------------ |
| …        | …      | …        | √解决意见           |                     |                    |                    |
| …        | …      | …        | X                   | √解决意见           |                    |                    |
| …        | …      | …        | X                   |                     | √解决意见          |                    |

支部/网格级别问题台账清单闭环程度

四、风险预警

能否根据数据情况，部分数据超出一定数值，钉钉客户端推送风险评估，实现风险预警

数值：问题处理时长

问题数量（提报率）

 

五、驾驶舱

美观、大气、展示数据分析界面及一些基础内容

树状图

 

六、季度总结、谈心谈话

1.生成格式表，网格长填报季度工作

2.年初制定谈心谈话计划表，根据计划每月固定时间段提醒网格负责人提交谈话记录