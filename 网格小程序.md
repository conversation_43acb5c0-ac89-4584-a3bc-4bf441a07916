一、网格权限

1.网格划分：党委（一级）、支部（二级）、党小组（三级）、党员（四级）。

2.权限划分： 

一级：全部数据查看、查阅网格内全部问题及督办权限。（同党群部权限）

二级：支部内问题处理，支部内党员职工档案审核、编辑，查阅本支部三级及四级问题提交情况、解决情况及具体解决办法，问题向上提报。

三级：解决并终止四级网格提报问题，查阅本支部三级及四级问题提交情况、解决情况及具体解决办法，问题向上级网格流转。

四级：解决并终止结对人员提报问题权限，问题向上级网格流转，季度总结提交。

二、 一人一档

1.专项档案：基础信息（姓名、岗位、政治面貌）。健康（病史、体检记录）、工作、家庭（成员构成、特殊情况）、生活（住房、经济状况）。

2.功能需求：满足多端录入，支持扫码填写（二维码生成）、后台导入、管理员修改等。

3.动态更新：支部定期核查，触发更新提醒（钉钉消息推送）。

4.隐私保护：做好每级网格员权限设置。

三、 分层赋色

***\*1.能否实现自动分类\****：***\*红色\****：存在重大疾病、低收入（低于市平均60%）、单亲/失独家庭。***\*黄色\****：慢性病、收入波动大、多子女抚养压力。***\*蓝色\****：无特殊困难职工。

2.后台手动调整通道

四、 问题反馈

***\*1.提交\****：职工通过表单（实名/匿名）提交问题，支持附件上传。

***\*2.智能分流\****：将现有小程序挂靠，未设立小程序的进行分流。共分为党员1+N、信访举报、合理化建议、安全管理、后勤服务等功能选项（后期需能变更），根据填报人分类，分流至不同人群，接收人可有二次分流权限。

3.***\*处理跟踪\****：各环节记录处理日志，一级网格及党群部可实时查看进度。

***\*5. 数据分析\****

***\*1.月度分析\****：

数据需包含提报人、所属支部、提报问题、提报分类、问题解决层级、解决意见、解决时间；

该支部当月收到问题总数、问题解决率、解决层级；

该职工提报问题数

***\*6.季度总结\****

网格员填写总结模板（含帮扶案例、问题解决数），系统自动计算积分（解决数量×权重 + 满意度评分）形成全公司排名，每月计算出前三名。